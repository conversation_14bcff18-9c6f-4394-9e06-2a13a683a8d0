[2025-08-15 08:35:04] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-15 08:35:50] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-15 08:36:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-15 08:39:04] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-15 08:41:04] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-15 09:05:41] local.ERROR: {"query":{"statusType":"IN_PROCESS"},"parameter":{"statusType":"IN_PROCESS"},"error":"Illuminate\\Validation\\ValidationException","message":"The selected status type is invalid.","code":0}  
[2025-08-15 09:05:49] local.ERROR: {"query":{"statusType":"IN_PROCESS"},"parameter":{"statusType":"IN_PROCESS"},"error":"Illuminate\\Validation\\ValidationException","message":"The selected status type is invalid.","code":0}  
[2025-08-15 09:05:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-15 09:05:59] local.ERROR: {"query":{"statusType":"IN_PROCESS"},"parameter":{"statusType":"IN_PROCESS"},"error":"Illuminate\\Validation\\ValidationException","message":"The selected status type is invalid.","code":0}  
[2025-08-18 01:54:46] local.INFO: user login from 127.0.0.1  
[2025-08-18 03:03:24] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-18 05:40:21] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-08-18 05:40:29] local.INFO: user login from 127.0.0.1  
[2025-08-18 05:47:05] local.ERROR: {"query":[],"parameter":{"domainName":"hundertelas.net","userEmail":"<EMAIL>","domainId":123,"createdDate":"2025-08-15 06:49:21","userID":7,"reason":"tes rtset estsetset set setset set set"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column users.name does not exist
LINE 1: ...elect \"users\".\"id\" as \"user_id\", \"users\".\"email\", \"users\".\"n...
                                                             ^ (Connection: client, SQL: select \"users\".\"id\" as \"user_id\", \"users\".\"email\", \"users\".\"name\" from \"registered_domains\" inner join \"user_contacts\" on \"user_contacts\".\"id\" = \"registered_domains\".\"user_contact_registrar_id\" inner join \"users\" on \"users\".\"id\" = \"user_contacts\".\"user_id\" inner join \"domains\" on \"domains\".\"id\" = \"registered_domains\".\"domain_id\" where \"domains\".\"id\" = 123 limit 1)","code":"42703"}  
[2025-08-18 05:47:26] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-18 05:54:06] local.ERROR: {"query":[],"parameter":{"domainName":"underscore.net","userEmail":"<EMAIL>","domainId":5,"createdDate":"2025-07-23 06:22:23","userID":7,"reason":"tes tsda asd qwd wad wad aw"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"user_email\" of relation \"email_histories\" does not exist
LINE 1: insert into \"email_histories\" (\"user_id\", \"user_email\", \"ful...
                                                  ^ (Connection: client, SQL: insert into \"email_histories\" (\"user_id\", \"user_email\", \"full_name\", \"domain_id\", \"reason\", \"created_at\", \"updated_at\") values (7, <EMAIL>, Julius Coloma, 5, {\"subject\":\"Domain Deletion Request Approved\",\"greeting\":\"Greetings!\",\"body\":\"Your request to delete the domain \\\"underscore.net\\\" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.\",\"text\":\"2025-08-18 05:54:00\",\"sender\":\"StrangeDomains Support\"}, 2025-08-18 05:54:00, 2025-08-18 05:54:00))","code":"42703"}  
[2025-08-18 05:54:06] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-18 05:57:25] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-18 05:57:53] local.ERROR: {"query":[],"parameter":{"domainName":"underscore.org","userEmail":"<EMAIL>","domainId":6,"createdDate":"2025-07-23 06:06:44","userID":7,"reason":"acsnjsazjicasd"},"error":"TypeError","message":"Unsupported operand types: string + string","code":0}  
[2025-08-18 05:58:59] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  

<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Services\DomainRequestDeleteEppService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Exception;
use Throwable;

class DomainRequestDeleteJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $params;

    /**
     * if process takes longer than indicated timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $domainId,
        string $domainName,
        array $requestData
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'domainId' => $domainId,
            'domainName' => $domainName,
            'requestData' => $requestData
        ];

        $this->onConnection(QueueConnection::DOMAIN_DELETION);
        $this->onQueue(QueueTypes::DOMAIN_DELETION[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): string
    {
        return 'domain_request_delete_' . $this->params['domainId'];
    }

    public function handle(): void
    {
        try {
            DomainRequestDeleteEppService::instance()->processEppDeletion($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            app(AuthLogger::class)->info('number of attempts: ' . $this->attempts());
            throw $e;
        }
    }

    public function backoff(): array
    {
        return [5, 10];
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error('DomainRequestDeleteJob failed: ' . $exception->getMessage());
    }
}

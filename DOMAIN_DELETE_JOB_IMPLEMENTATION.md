# Domain Delete Job Implementation

## Overview
This implementation converts the EPP call process in domain deletion to use a job-based approach with EPP info validation, following the established patterns in the codebase.

## Key Features
1. **EPP Info Check**: Before deletion, checks EPP domain info to validate if deletion is allowed
2. **clientDeleteProhibited Protection**: Prevents deletion if domain has `clientDeleteProhibited` status
3. **Job-based Processing**: Uses Laravel queues for asynchronous processing
4. **Error Handling**: Comprehensive error logging and handling
5. **Follows Existing Patterns**: Uses the same structure as other domain jobs in the codebase

## Files Created/Modified

### New Files Created:
1. `app/Modules/RequestDelete/Jobs/DomainRequestDeleteJob.php` - Main job class
2. `app/Modules/RequestDelete/Services/DomainRequestDeleteJobService.php` - Job dispatch service
3. `app/Modules/RequestDelete/Services/DomainRequestDeleteEppService.php` - EPP processing service
4. `tests/Unit/DomainRequestDeleteJobTest.php` - Unit tests

### Modified Files:
1. `app/Modules/RequestDelete/Services/DomainDeleteService.php` - Updated to dispatch job instead of direct EPP calls

## Implementation Details

### Job Flow:
1. **Domain Delete Request Approved** → `DomainDeleteService::approveDeleteRequestSave()`
2. **Job Dispatch** → `DomainRequestDeleteJobService::dispatch()`
3. **Job Processing** → `DomainRequestDeleteJob::handle()`
4. **EPP Processing** → `DomainRequestDeleteEppService::processEppDeletion()`

### EPP Validation Process:
1. **Get EPP Info**: Calls `EppDomainService::callEppDomainInfo()`
2. **Check Status**: Validates if `clientDeleteProhibited` is in status array
3. **Block if Prohibited**: Logs error and returns early if deletion is prohibited
4. **Proceed if Allowed**: Continues with EPP deletion calls

### Error Handling:
- EPP info call failures are logged and job terminates
- clientDeleteProhibited status blocks deletion with error log
- EPP delete call failures are logged and job terminates
- Datastore delete call failures are logged and job terminates

### Queue Configuration:
- Uses existing `domain_deletion_jobs` connection
- Routes to registry-specific queues (VERISIGN-DELETE, PIR-DELETE)
- Follows same pattern as `DomainEppDeletion` job

## Example EPP Info Response:
```json
{
  "status": "OK",
  "data": {
    "status": [
      "clientDeleteProhibited",
      "clientTransferProhibited", 
      "inactive"
    ]
  }
}
```

## Usage:
The job is automatically dispatched when a domain deletion request is approved through the admin interface. No manual intervention required.

## Testing:
Run the unit tests to verify the implementation:
```bash
php artisan test tests/Unit/DomainRequestDeleteJobTest.php
```

## Queue Worker:
The existing domain deletion queue worker will process these jobs:
```bash
php artisan queue:work domain_deletion_jobs --queue=VERISIGN-DELETE,PIR-DELETE --tries=3 --timeout=120
```

## Benefits:
1. **Asynchronous Processing**: Domain deletions don't block the web interface
2. **Better Error Handling**: Comprehensive logging and retry mechanisms
3. **Status Validation**: Prevents deletion of protected domains
4. **Scalability**: Can handle multiple deletions concurrently
5. **Consistency**: Follows established job patterns in the codebase

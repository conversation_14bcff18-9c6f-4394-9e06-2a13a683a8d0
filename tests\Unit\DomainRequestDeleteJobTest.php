<?php

namespace Tests\Unit;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\RequestDelete\Jobs\DomainRequestDeleteJob;
use App\Modules\RequestDelete\Services\DomainRequestDeleteEppService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;
use Mockery;

class DomainRequestDeleteJobTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Queue::fake();
    }

    public function test_job_can_be_dispatched()
    {
        $job = new DomainRequestDeleteJob(
            '123',
            'example.com',
            ['domainId' => '123', 'userID' => '456']
        );

        $this->assertInstanceOf(DomainRequestDeleteJob::class, $job);
    }

    public function test_job_handles_client_delete_prohibited_status()
    {
        // Mock the EppDomainService
        $eppServiceMock = Mockery::mock(EppDomainService::class);
        $eppServiceMock->shouldReceive('instance')->andReturnSelf();
        $eppServiceMock->shouldReceive('callEppDomainInfo')
            ->with('example.com')
            ->andReturn([
                'status' => 'OK',
                'data' => [
                    'status' => ['clientDeleteProhibited', 'inactive']
                ]
            ]);

        // Mock AuthLogger
        $loggerMock = Mockery::mock(AuthLogger::class);
        $loggerMock->shouldReceive('error')
            ->with('Domain example.com cannot be deleted: clientDeleteProhibited status is active')
            ->once();

        $this->app->instance(AuthLogger::class, $loggerMock);
        $this->app->instance(EppDomainService::class, $eppServiceMock);

        $service = new DomainRequestDeleteEppService();
        $service->processEppDeletion([
            'domainName' => 'example.com',
            'requestData' => ['domainId' => '123']
        ]);

        // The test passes if no exception is thrown and the error is logged
        $this->assertTrue(true);
    }

    public function test_job_handles_epp_info_error()
    {
        // Mock the EppDomainService to return error
        $eppServiceMock = Mockery::mock(EppDomainService::class);
        $eppServiceMock->shouldReceive('instance')->andReturnSelf();
        $eppServiceMock->shouldReceive('callEppDomainInfo')
            ->with('example.com')
            ->andReturn([
                'status' => 'error',
                'message' => 'EPP connection failed'
            ]);

        // Mock AuthLogger
        $loggerMock = Mockery::mock(AuthLogger::class);
        $loggerMock->shouldReceive('error')
            ->with('Failed to get EPP info for domain example.com: EPP connection failed')
            ->once();

        $this->app->instance(AuthLogger::class, $loggerMock);
        $this->app->instance(EppDomainService::class, $eppServiceMock);

        $service = new DomainRequestDeleteEppService();
        $service->processEppDeletion([
            'domainName' => 'example.com',
            'requestData' => ['domainId' => '123']
        ]);

        // The test passes if no exception is thrown and the error is logged
        $this->assertTrue(true);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

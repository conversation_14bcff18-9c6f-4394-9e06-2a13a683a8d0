<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainRequestDeleteJob;
use App\Util\Constant\QueueConnection;

class DomainRequestDeleteJobService
{
    private static ?self $instance = null;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function dispatch(array $data): void
    {
        $job = new DomainRequestDeleteJob(
            $data['domainId'],
            $data['domainName'],
            $data['requestData']
        );

        dispatch($job);
    }
}

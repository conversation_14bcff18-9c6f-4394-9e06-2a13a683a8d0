<?php

namespace App\Modules\RequestDelete\Services;

use App\Events\DomainHistoryEvent;
use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Services\EppDomainService;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainRequestDeleteEppService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function processEppDeletion(array $params): void
    {
        $domainName = $params['domainName'];
        $requestData = $params['requestData'];

        // First check EPP info
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($domainName);
        if ($eppInfo['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to get EPP info for domain {$domainName}: " . ($eppInfo['message'] ?? 'Unknown error'));
            return;
        }

        // Check if domain has clientDeleteProhibited status
        if (isset($eppInfo['data']['status']) && in_array('clientDeleteProhibited', $eppInfo['data']['status'])) {
            app(AuthLogger::class)->error("Domain {$domainName} cannot be deleted: clientDeleteProhibited status is active");
            return;
        }

        // Proceed with EPP deletion calls
        $eppDeleteResult = EppDomainService::instance()->callEppDomainDelete($domainName);
        if ($eppDeleteResult['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to delete domain {$domainName} via EPP: " . ($eppDeleteResult['message'] ?? 'Unknown error'));
            return;
        }

        $datastoreDeleteResult = EppDomainService::instance()->callDatastoreDomainDelete($domainName);
        if ($datastoreDeleteResult['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to delete domain {$domainName} from datastore: " . ($datastoreDeleteResult['message'] ?? 'Unknown error'));
            return;
        }

        // Process local deletion and notifications
        $this->localDelete($requestData);
        $this->userNotification($requestData);
        $this->userEmailNotification($requestData);
        $this->domainHistory($requestData);

        // Insert into pending domain deletions
        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $requestData['domainId'],
            'deleted_by' => Auth::user()->email ?? 'System',
            'deleted_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        app(AuthLogger::class)->info("Domain {$domainName} successfully deleted via EPP and processed locally");
    }

    private function localDelete($requestData, bool $skipUpdate = false)
    {
        if (!$skipUpdate) {
            $this->updateDomainDeletionRequestTable($requestData);
        }

        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $requestData['domainId'])
            ->update($updates);
    }

    private function updateDomainDeletionRequestTable($requestData, $authID = null)
    {
        $agentID = $authID ?? Auth::id();

        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($requestData['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->update([
                'support_agent_id'   => $agentID,
                'support_agent_name'=> Auth::user()->name . ' (' . Auth::user()->email . ')',
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $requestData['support_note'] ?? 'Support note not provided',
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function userNotification($data)
    {
        $user = DB::client()->table('users')->where('id', $data['userID'])->first();

        if (!$user) {
            return;
        }

        DB::client()->table('notifications')->insert([
            'user_id' => $data['userID'],
            'title' => 'Domain Deletion Request',
            'message' => 'Your domain deletion request for ' . $data['domainName'] . ' has been approved.',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function userEmailNotification($data)
    {
        $user = DB::client()->table('users')->where('id', $data['userID'])->first();

        if (!$user) {
            return;
        }

        try {
            Mail::to($user->email)->send(new UserDeleteRequestMail($data));
        } catch (\Exception $e) {
            app(AuthLogger::class)->error("Failed to send deletion email to {$user->email}: " . $e->getMessage());
        }
    }

    private function domainHistory($data)
    {
        event(new DomainHistoryEvent($data['domainId'], 'Domain Deleted', $data));
    }
}
